// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import {DarwinVaultFactory} from "src/DarwinVaultFactory.sol";
import {MockERC20} from "test/utils/MockERC20.sol";
import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";

contract DarwinVaultFactoryTest is Test {
    DarwinVaultFactory factory;
    MockERC20 steth;
    address alice = address(0x1);

    event VaultDeployed(address indexed vault, address indexed asset);

    function setUp() public {
        factory = new DarwinVaultFactory();
        steth = new MockERC20("stETH", "stETH");
    }

    function testOnlyOwnerCreateVault() public {
        vm.prank(alice);
        vm.expectRevert(abi.encodeWithSelector(Ownable.OwnableUnauthorizedAccount.selector, alice));
        factory.createVault(address(steth));
    }

    function testCreateVaultEmitsEvent() public {
        vm.expectEmit(true, true, false, false);
        emit VaultDeployed(address(0), address(steth));
        factory.createVault(address(steth));
    }

    function testIsRegisteredVaultFalse() public {
        assertEq(factory.isRegisteredVault(address(steth)), false);
    }
}
