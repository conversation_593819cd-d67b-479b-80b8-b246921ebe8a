// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import {DolpinToken} from "src/DolpinToken.sol";

contract DolpinTokenTest is Test {
    DolpinToken token;
    address alice = address(0x1);
    address bob = address(0x2);

    function setUp() public {
        token = new DolpinToken();
        token.grantRole(token.MINTER_ROLE(), address(this));
    }

    function testInitialMint() public {
        assertEq(token.totalSupply(), token.LIQUIDITY_ALLOCATION());
        assertEq(token.balanceOf(address(this)), token.LIQUIDITY_ALLOCATION());
    }

    function testCreateAndClaimVesting() public {
        token.createVestingSchedule(alice, 100 ether, 1 days, 10 days);
        vm.warp(block.timestamp + 2 days);
        vm.prank(alice);
        uint256 claimable = token.claimVestedTokens();
        assertGt(claimable, 0);
        assertEq(token.balanceOf(alice), claimable);
    }

    function testMintRole() public {
        vm.prank(address(this));
        token.mint(bob, 50 ether);
        assertEq(token.balanceOf(bob), 50 ether);
    }

    function testBurnForYield() public {
        token.transfer(alice, 10 ether);
        vm.prank(alice);
        token.burnForYield(5 ether);
        assertEq(token.balanceOf(alice), 5 ether);
        assertEq(token.getTotalBurned(), 5 ether);
    }
}
