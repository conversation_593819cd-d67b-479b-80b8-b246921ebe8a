// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import {BurnPool} from "src/BurnPool.sol";
import {DolpinToken} from "src/DolpinToken.sol";
import {MockERC20} from "test/utils/MockERC20.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract BurnPoolTest is Test {
    DolpinToken token;
    BurnPool pool;
    MockERC20 reward;
    address alice = address(0x1);

    function setUp() public {
        token = new DolpinToken();
        reward = new MockERC20("R", "R");
        uint256 endTime = block.timestamp + 1 days;
        pool = new BurnPool(IERC20(address(token)), endTime, address(this));
        token.transfer(alice, 100 ether);
        vm.startPrank(alice);
        token.approve(address(pool), type(uint256).max);
        vm.stopPrank();
    }

    function testDepositAndBurnFlow() public {
        vm.prank(alice);
        pool.deposit(10 ether);
        assertEq(pool.totalDeposited(), 10 ether);
        vm.warp(pool.burnEndTime() + 1);
        vm.expectRevert(abi.encodeWithSignature("ERC20InvalidReceiver(address)", address(0)));
        pool.burn();
    }

    function testCreateRoundBeforeBurnReverts() public {
        vm.prank(alice);
        pool.deposit(10 ether);
        uint256 start = block.timestamp + 1;
        uint256 end = start + 1 days;
        vm.expectRevert("Not burned yet");
        pool.createRound(start, end);
    }
}
