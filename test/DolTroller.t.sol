// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import {DolTroller} from "src/DolTroller.sol";
import {DstETH} from "src/vaults/DstETH.sol";
import {MockERC20} from "test/utils/MockERC20.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract DolTrollerTest is Test {
    DolTroller troller;
    MockERC20 reward;
    DstETH vault;
    MockERC20 steth;
    MockERC20 weth;
    address alice = address(0x1);

    function setUp() public {
        reward = new MockERC20("DOL", "DOL");
        troller = new DolTroller(IERC20(address(reward)), address(this));
        steth = new MockERC20("stETH", "stETH");
        weth = new MockERC20("WETH", "WETH");
        vault = new DstETH(address(steth), address(weth), troller);
        troller.listMarket(vault);
        steth.mint(alice, 100 ether);
        vm.prank(alice);
        steth.approve(address(vault), type(uint256).max);
    }

    function testListMarket() public {
        assertTrue(troller.isMarketListed(address(vault)));
    }

    function testDepositAndWithdraw() public {
        vm.prank(alice);
        vault.deposit(10 ether);
        assertEq(vault.balanceOf(alice), 10);
        vm.prank(alice);
        vault.withdraw(10 ether);
        assertEq(vault.balanceOf(alice), 0);
    }
}
