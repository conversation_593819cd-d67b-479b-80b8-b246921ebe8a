# DsUSDS 通用收益代币金库 - 使用指南

## 概述

DsUSDS 是 Dolpin Finance 协议中的通用收益代币金库合约，设计用于支持多种收益代币类型，包括 sUSDS (Sky Protocol) 和 stETH (Lido)。它通过统一的架构设计，参考了 DstETH 的简洁设计模式，并扩展支持多种资产类型。

## 核心特性

### 🎯 统一架构设计
- **多资产支持**: 一个合约同时支持 sUSDS 和 stETH
- **参考 DstETH**: 保持与现有 DstETH 设计的一致性
- **可配置性**: 通过构造函数参数配置不同资产类型

### 🔄 智能收益管理
- **Rebasing 资产**: 支持 stETH 等 rebasing 代币的收益计算
- **价格基础资产**: 支持 sUSDS 等基于价格变化的收益代币
- **自动收益收集**: 可配置的自动收益收集机制

### 💰 灵活的收益分配
- **协议分成**: 可配置的协议收益分成 (默认 10%)
- **用户收益**: 剩余收益归用户所有
- **回购集成**: 与 BurnPool 无缝集成

## 技术架构

### 继承关系
```
DsUSDS
├── DVault (基础金库功能)
├── AccessControl (角色权限管理)
└── ReentrancyGuard (重入保护)
```

### 资产类型支持
```solidity
enum AssetType {
    SUSDS,    // Sky Protocol sUSDS
    STETH     // Lido stETH
}
```

## 主要合约

### 文件结构
```
src/vaults/
├── DsUSDS.sol             # 通用收益代币金库 (本文件)
├── DVault.sol             # 基础金库合约
└── DstETH.sol             # 原 stETH 金库 (可替换)

docs/
├── DsUSDS_Documentation.md    # 详细技术文档
└── README_DsUSDS.md          # 本使用指南
```

## 部署指南

### 1. sUSDS 金库部署

```solidity
// 部署 sUSDS 金库
DsUSDS sUSdsVault = new DsUSDS(
    0x...,                  // sUSDS 代币地址
    0x...,                  // USDS 代币地址
    IDolTroller(0x...),     // DolTroller 地址
    AssetType.SUSDS,        // 资产类型: sUSDS
    "sUSDS"                 // 资产名称
);
```

### 2. stETH 金库部署

```solidity
// 部署 stETH 金库 (替换原 DstETH)
DsUSDS stETHVault = new DsUSDS(
    0x...,                  // stETH 代币地址
    0x...,                  // ETH 代币地址 (或 WETH)
    IDolTroller(0x...),     // DolTroller 地址
    AssetType.STETH,        // 资产类型: stETH
    "stETH"                 // 资产名称
);
```

### 3. 初始配置

```solidity
// 设置价格预言机
vault.setPriceOracle(0x...);

// 配置角色权限
vault.grantRole(vault.YIELD_MANAGER_ROLE(), yieldManagerAddress);
vault.grantRole(vault.ORACLE_MANAGER_ROLE(), oracleManagerAddress);

// 设置费用参数
vault.setDepositFee(0);      // 0% 存款费用
vault.setWithdrawalFee(0);   // 0% 提款费用
vault.setBuybackFee(1000);   // 10% 回购费用

// 设置协议收益分成
vault.setProtocolYieldShare(1000); // 10% 协议分成

// 启用自动收益收集
vault.setAutoYieldCollection(true);
```

## 核心接口

### 用户接口
```solidity
// 存入收益代币
function deposit(uint256 amount) external;

// 提取收益代币
function withdraw(uint256 amount) external;

// 查看最大存款限额
function maxDeposit(address owner) external view returns (uint256);

// 查看最大提款限额
function maxWithdraw(address owner) external view returns (uint256);
```

### 管理接口
```solidity
// 收集收益 (YIELD_MANAGER_ROLE)
function collectYield() external;

// 设置价格预言机 (ORACLE_MANAGER_ROLE)
function setPriceOracle(address _priceOracle) external;

// 设置协议收益分成 (Owner)
function setProtocolYieldShare(uint256 _protocolYieldShare) external;

// 设置自动收益收集 (Owner)
function setAutoYieldCollection(bool _enabled) external;
```

### 查询接口
```solidity
// 获取当前价格
function getCurrentPrice() external view returns (uint256);

// 获取收益统计
function getYieldStats() external view returns (uint256, uint256, uint256);

// 获取资产配置
function getAssetConfig() external view returns (...);

// 获取合约信息
function getContractInfo() external view returns (string memory);
```

## 使用示例

### 用户存款 (sUSDS)
```solidity
// 1. 用户授权 sUSDS
IERC20(sUSdsToken).approve(address(sUSdsVault), amount);

// 2. 存入 sUSDS
sUSdsVault.deposit(amount);

// 用户现在开始赚取 sUSDS 收益和 Dolpin 奖励
```

### 用户存款 (stETH)
```solidity
// 1. 用户授权 stETH
IERC20(stETHToken).approve(address(stETHVault), amount);

// 2. 存入 stETH
stETHVault.deposit(amount);

// 用户现在开始赚取 stETH rebasing 收益和 Dolpin 奖励
```

### 用户提款
```solidity
// 提取收益代币
vault.withdraw(amount);

// 资金将转给用户，扣除相应费用
```

### 收益管理
```solidity
// 收益管理员收集收益
vault.collectYield();

// 查看收益统计
(uint256 totalYield, uint256 protocolYield, uint256 userYield) = vault.getYieldStats();
```

## 收益机制

### stETH 收益计算 (Rebasing 模式)
```
收益 = 当前 stETH 余额 - 收益基准线
基准线在每次存款/提款时更新
```

### sUSDS 收益计算 (价格基础模式)
```
当前价值 = stETH 余额 × 当前价格
预期价值 = 用户本金总和
收益 = (当前价值 - 预期价值) 转换为 yToken
```

### 收益分配
```
协议分成 = 总收益 × protocolYieldShare / 10000
用户分成 = 总收益 - 协议分成
```

## 安全特性

### 1. 访问控制
- **YIELD_MANAGER_ROLE**: 收益管理权限
- **ORACLE_MANAGER_ROLE**: 价格预言机管理权限
- **DEFAULT_ADMIN_ROLE**: 超级管理员权限

### 2. 参数限制
- 最大协议收益分成: 30%
- 最小收益收集间隔: 1小时
- 重入保护: 所有状态变更函数

### 3. 紧急机制
- 紧急恢复功能 (不能恢复 yToken 和 pToken)
- 参数验证和边界检查

## 监控和维护

### 关键指标
- **TVL**: 总锁定价值
- **收益率**: 年化收益率
- **用户数量**: 活跃用户统计
- **收益分配**: 协议 vs 用户收益比例

### 运维任务
- **收益收集**: 定期触发收益收集
- **价格监控**: 监控价格预言机状态
- **参数调整**: 根据市场情况调整费用和分成
- **安全监控**: 监控异常交易和攻击

## 与 DstETH 的关系

### 替换方案
DsUSDS 可以完全替换现有的 DstETH 合约：

```solidity
// 原 DstETH 部署
DstETH oldVault = new DstETH(stETHAddress, ethAddress, dolTroller);

// 新 DsUSDS 部署 (功能更强大)
DsUSDS newVault = new DsUSDS(
    stETHAddress, 
    ethAddress, 
    dolTroller, 
    AssetType.STETH, 
    "stETH"
);
```

### 兼容性
- **完全兼容**: 所有 DstETH 的功能都被保留
- **功能增强**: 增加了收益管理、价格预言机等功能
- **接口一致**: 保持与 DVault 的接口兼容性

## 测试

### 运行测试
```bash
# 编译合约
forge build

# 运行测试
forge test

# 运行特定测试
forge test --match-contract DsUSDS
```

### 测试覆盖
- 存款/提款功能测试
- 收益收集机制测试
- 多资产类型测试
- 权限控制测试
- 边界条件测试

## 升级路径

### 从 DstETH 迁移
1. 部署新的 DsUSDS 合约 (STETH 类型)
2. 在 DolTroller 中更新市场配置
3. 用户资金迁移 (可选)
4. 停用旧的 DstETH 合约

### 添加新资产类型
1. 扩展 AssetType 枚举
2. 添加对应的价格获取逻辑
3. 实现资产特定的收益计算
4. 部署新的金库实例

## 总结

DsUSDS 合约通过统一的架构设计，成功整合了 DstETH 的设计理念，并扩展支持多种收益代币类型。它为 Dolpin Finance 协议提供了：

- **统一的金库解决方案**: 一个合约支持多种资产
- **增强的功能**: 相比原 DstETH 增加了更多功能
- **完全的兼容性**: 保持与现有系统的兼容
- **灵活的扩展性**: 易于添加新的资产类型

这使得协议能够更高效地管理多种收益代币，同时为用户提供更好的收益优化体验。

---

**⚠️ 重要提示**: 
1. 本合约尚未经过正式审计，请在生产环境使用前进行充分测试
2. 请确保理解所有风险后再进行大额资金操作
3. 建议在主网部署前进行全面的安全审计
