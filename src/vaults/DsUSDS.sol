// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {DVault} from "./DVault.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {IERC20Metadata} from "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol";
import {SafeERC20} from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import {IDolTroller} from "../interfaces/IDolTroller.sol";

/**
 * @title DsUSDS
 * @notice DVault implementation for sUSDS (Sky Protocol USD Savings) where users deposit sUSDS and earn yield
 * @dev Implementation follows DstETH pattern with price oracle integration for sUSDS/USDS conversion
 */
contract DsUSDS is DVault {
    using SafeERC20 for IERC20;

    // ============ State Variables ============
    
    // Token addresses
    address public immutable sUSDS=******************************************;  // yToken - Sky Protocol USD Savings token
    address public immutable USDS=******************************************;   // pToken - Sky Protocol USD token (pricing token)
    
    // Price oracle for sUSDS/USDS conversion rate
    address public priceOracle;
    
    // Settlement tracking
    uint256 public settlementAmount;
    
    // Price precision constant (18 decimals)
    uint256 public constant PRICE_PRECISION = 1e18;
    
    // Default exchange rate (1:1) used when oracle is not available
    uint256 public constant DEFAULT_EXCHANGE_RATE = 1e18;

    // ============ Events ============
    
    event PriceOracleUpdated(address indexed oldOracle, address indexed newOracle);
    event PriceUpdated(uint256 oldPrice, uint256 newPrice);

    // ============ Constructor ============

    constructor(
        address _sUSDS,
        address _USDS,
        IDolTroller _dolTroller
    ) DVault(_sUSDS, _USDS, _dolTroller) {
        require(_sUSDS != address(0), "Invalid sUSDS address");
        require(_USDS != address(0), "Invalid USDS address");
        
        sUSDS = _sUSDS;
        USDS = _USDS;
    }

    // ============ Asset Functions ============

    /**
     * @notice Get the yToken address (sUSDS)
     * @return The sUSDS token address
     */
    function yToken() external view override returns (address) {
        return sUSDS;
    }

    /**
     * @notice Get the pToken address (USDS) 
     * @return The USDS token address (pricing token)
     */
    function pToken() external view override returns (address) {
        return USDS;
    }

    /**
     * @notice Get the bToken address (buyback token)
     * @return The USDS token address (used for buyback)
     * @dev For sUSDS vault, bToken is USDS to provide stable value for buyback
     */
    function bToken() public view override returns (address) {
        // bToken is the same as yToken for this implementation
        return sUSDS;
    }

    // ============ Price Functions ============

    /**
     * @notice Get the current price of sUSDS in terms of USDS
     * @return The current exchange rate (USDS per sUSDS)
     * @dev This is the core pricing function that determines sUSDS/USDS conversion rate
     */
    function getYTokenPrice() public view override returns (uint256) {
        if (priceOracle != address(0)) {
            try this._getOraclePrice() returns (uint256 oraclePrice) {
                if (oraclePrice > 0) {
                    return oraclePrice;
                }
            } catch {
                // Oracle failed, fall back to default rate
            }
        }
        
        // Return default 1:1 exchange rate if oracle is not available or fails
        return DEFAULT_EXCHANGE_RATE;
    }

    /**
     * @notice Get price from oracle (external call for try-catch)
     * @return The oracle price
     * @dev This function is external to enable try-catch in getYTokenPrice()
     */
    function _getOraclePrice() external view returns (uint256) {
        require(priceOracle != address(0), "Price oracle not set");
        
        // Call the price oracle to get sUSDS/USDS exchange rate
        // This should be replaced with actual oracle interface call
        // Example: return IPriceOracle(priceOracle).getPrice(sUSDS, USDS);
        // Example: return ISkyProtocol(priceOracle).getExchangeRate();
        
        // Placeholder implementation - should be replaced with actual oracle call
        return DEFAULT_EXCHANGE_RATE;
    }

    // ============ Settlement Functions ============

    /**
     * @notice Settlement function for yield distribution
     * @param roundId The settlement round ID
     * @param amountToSettle Amount of sUSDS to settle
     * @return success Whether settlement was successful
     * @dev Converts sUSDS yield to USDS for buyback pool
     */
    function settleYield(uint256 roundId, uint256 amountToSettle) public override onlyRightRound(roundId) returns (bool) {
        require(amountToSettle > 0, "Settlement amount must be positive");
        
        // Convert sUSDS amount to USDS equivalent for settlement
        uint256 usdsEquivalent = yTokenToPToken(amountToSettle);
        
        // Check if we have enough sUSDS balance
        uint256 sUSdsBalance = IERC20(sUSDS).balanceOf(address(this));
        require(sUSdsBalance >= amountToSettle, "Insufficient sUSDS balance");
        
        // Store settlement amount in USDS terms for buyback
        settlementAmount = usdsEquivalent;
        
        return true;
    }

    /**
     * @notice Get the bToken amount available for transfer to buyback pool
     * @param roundId The settlement round ID
     * @return The amount of USDS available for buyback
     * @dev Returns USDS equivalent of settled sUSDS yield
     */
    function getBTokenAmount(uint256 roundId) public view override onlyRightRound(roundId) returns (uint256) {
        return settlementAmount;
    }

    // ============ Admin Functions ============

    /**
     * @notice Set the price oracle address
     * @param _priceOracle The new price oracle address
     * @dev Only owner can update the price oracle
     */
    function setPriceOracle(address _priceOracle) external onlyOwner {
        address oldOracle = priceOracle;
        priceOracle = _priceOracle;
        emit PriceOracleUpdated(oldOracle, _priceOracle);
    }

    /**
     * @notice Update the current price (manual override)
     * @dev Emergency function to manually update price if oracle fails
     */
    function updatePrice() external onlyOwner {
        uint256 oldPrice = getYTokenPrice();
        // This would trigger a price update event
        emit PriceUpdated(oldPrice, getYTokenPrice());
    }

    // ============ View Functions ============

    /**
     * @notice Get the contract name for identification
     * @return The contract name
     */
    function name() external pure returns (string memory) {
        return "DsUSDS Vault";
    }

    /**
     * @notice Get the contract symbol for identification  
     * @return The contract symbol
     */
    function symbol() external pure returns (string memory) {
        return "DsUSDS";
    }

    /**
     * @notice Get the total assets under management
     * @return Total sUSDS balance in the contract
     */
    function totalAssets() external view returns (uint256) {
        return IERC20(sUSDS).balanceOf(address(this));
    }

    /**
     * @notice Get the current sUSDS/USDS exchange rate from oracle
     * @return The current exchange rate
     */
    function getCurrentExchangeRate() external view returns (uint256) {
        return getYTokenPrice();
    }

    /**
     * @notice Get price oracle configuration
     * @return oracle The current price oracle address
     * @return hasOracle Whether oracle is configured
     */
    function getPriceOracleInfo() external view returns (address oracle, bool hasOracle) {
        return (priceOracle, priceOracle != address(0));
    }

    /**
     * @notice Preview the USDS amount that would be received for a given sUSDS amount
     * @param sUSdsAmount The amount of sUSDS
     * @return usdsAmount The equivalent USDS amount
     */
    function previewConversion(uint256 sUSdsAmount) external view returns (uint256 usdsAmount) {
        return yTokenToPToken(sUSdsAmount);
    }

    /**
     * @notice Get detailed vault information
     * @return sUSdsBalance Total sUSDS in vault
     * @return totalPrincipal Total user principal in USDS terms
     * @return currentYield Current yield available
     * @return exchangeRate Current sUSDS/USDS rate
     */
    function getVaultInfo() external view returns (
        uint256 sUSdsBalance,
        uint256 totalPrincipal, 
        uint256 currentYield,
        uint256 exchangeRate
    ) {
        sUSdsBalance = IERC20(sUSDS).balanceOf(address(this));
        totalPrincipal = totalPTokensStaked;
        
        // Calculate current yield: current value minus original principal
        uint256 currentValueInUSDS = yTokenToPToken(sUSdsBalance);
        currentYield = currentValueInUSDS > totalPrincipal ? 
            currentValueInUSDS - totalPrincipal : 0;
            
        exchangeRate = getYTokenPrice();
    }
}
