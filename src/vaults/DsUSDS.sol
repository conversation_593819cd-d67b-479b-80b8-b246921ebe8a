// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {DVault} from "./DVault.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {IERC20Metadata} from "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol";
import {SafeERC20} from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import {AccessControl} from "@openzeppelin/contracts/access/AccessControl.sol";
import {ReentrancyGuard} from "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import {Math} from "@openzeppelin/contracts/utils/math/Math.sol";

import {IDolTroller} from "../interfaces/IDolTroller.sol";

/**
 * @title DsUSDS - Dolpin 通用收益代币金库 (支持 sUSDS 和 stETH)
 * @notice 统一的收益代币金库实现，支持多种收益代币类型
 * @dev 核心功能：
 *      1. 支持 sUSDS (Sky Protocol) 和 stETH (Lido) 等收益代币
 *      2. 自动收益捕获和分配机制
 *      3. 与 DolTroller 集成提供 Dolpin 代币奖励
 *      4. 灵活的价格预言机支持
 *      5. 完整的费用管理和收益分成系统
 * 
 * 设计理念：
 *      - 参考 DstETH 的简洁设计模式
 *      - 通过配置参数支持不同资产类型
 *      - 保持与现有 DVault 架构的完全兼容
 * 
 * <AUTHOR> Finance Team
 * @custom:version 1.0.0
 * @custom:security-contact <EMAIL>
 */
contract DsUSDS is DVault, AccessControl, ReentrancyGuard {
    using SafeERC20 for IERC20;
    using Math for uint256;

    /*//////////////////////////////////////////////////////////////
                                CONSTANTS
    //////////////////////////////////////////////////////////////*/
    
    /// @notice 角色：收益管理员，可以触发收益收集和管理
    bytes32 public constant YIELD_MANAGER_ROLE = keccak256("YIELD_MANAGER_ROLE");
    
    /// @notice 角色：价格预言机管理员，可以更新价格源
    bytes32 public constant ORACLE_MANAGER_ROLE = keccak256("ORACLE_MANAGER_ROLE");
    
    /// @notice 精度常量，用于价格计算 (18位精度)
    uint256 public constant PRICE_PRECISION = 1e18;
    
    /// @notice 收益收集的最小间隔时间 (1小时)
    uint256 public constant MIN_YIELD_COLLECTION_INTERVAL = 1 hours;
    
    /// @notice 最大协议收益分成 (30%)
    uint256 public constant MAX_PROTOCOL_YIELD_SHARE = 3000;

    /*//////////////////////////////////////////////////////////////
                            STATE VARIABLES
    //////////////////////////////////////////////////////////////*/
    
    /// @notice 资产类型枚举
    enum AssetType {
        SUSDS,    // Sky Protocol sUSDS
        STETH     // Lido stETH
    }
    
    /// @notice 当前资产类型
    AssetType public immutable assetType;
    
    /// @notice 资产名称 (用于标识和日志)
    string public assetName;
    
    /// @notice 上次收益收集的时间戳
    uint256 public lastYieldCollection;
    
    /// @notice 累积的协议收益 (以 yToken 计价)
    uint256 public accumulatedProtocolYield;
    
    /// @notice 价格预言机地址 (用于获取 yToken/pToken 汇率)
    address public priceOracle;
    
    /// @notice 是否启用自动收益收集
    bool public autoYieldCollection;
    
    /// @notice 协议收益分成比例 (基点，默认10% = 1000)
    uint256 public protocolYieldShare;
    
    /// @notice 收益基准线 (用于计算收益增长)
    uint256 public yieldBaseline;

    /*//////////////////////////////////////////////////////////////
                                EVENTS
    //////////////////////////////////////////////////////////////*/
    
    /// @notice 收益收集事件
    event YieldCollected(
        uint256 totalYield,
        uint256 protocolShare,
        uint256 userShare,
        uint256 timestamp
    );
    
    /// @notice 价格预言机更新事件
    event PriceOracleUpdated(address indexed oldOracle, address indexed newOracle);
    
    /// @notice 协议收益分成更新事件
    event ProtocolYieldShareUpdated(uint256 oldShare, uint256 newShare);
    
    /// @notice 自动收益收集状态更新事件
    event AutoYieldCollectionUpdated(bool enabled);
    
    /// @notice 收益基准线更新事件
    event YieldBaselineUpdated(uint256 oldBaseline, uint256 newBaseline);

    /*//////////////////////////////////////////////////////////////
                                ERRORS
    //////////////////////////////////////////////////////////////*/
    
    error YieldCollectionTooFrequent();
    error InvalidProtocolYieldShare();
    error InsufficientYieldToCollect();

    /*//////////////////////////////////////////////////////////////
                              CONSTRUCTOR
    //////////////////////////////////////////////////////////////*/
    
    /**
     * @notice 构造函数
     * @param _yToken 收益代币地址 (如 sUSDS, stETH)
     * @param _pToken 基础代币地址 (如 USDS, ETH)
     * @param _dolTroller DolTroller 合约地址
     * @param _assetType 资产类型 (SUSDS 或 STETH)
     * @param _assetName 资产名称
     */
    constructor(
        address _yToken,
        address _pToken,
        IDolTroller _dolTroller,
        AssetType _assetType,
        string memory _assetName
    ) DVault(_yToken, _pToken, _dolTroller) {
        require(_yToken != address(0), "Invalid yToken address");
        require(_pToken != address(0), "Invalid pToken address");
        require(bytes(_assetName).length > 0, "Asset name cannot be empty");
        
        // 设置资产配置
        assetType = _assetType;
        assetName = _assetName;
        
        // 设置默认值
        protocolYieldShare = 1000; // 10%
        autoYieldCollection = true;
        lastYieldCollection = block.timestamp;
        
        // 初始化收益基准线
        if (_assetType == AssetType.STETH) {
            // 对于 stETH (rebasing 资产)，基准线为当前余额
            yieldBaseline = IERC20(_yToken).balanceOf(address(this));
        } else {
            // 对于 sUSDS (非 rebasing 资产)，基准线为 0
            yieldBaseline = 0;
        }
        
        // 设置角色
        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _grantRole(YIELD_MANAGER_ROLE, msg.sender);
        _grantRole(ORACLE_MANAGER_ROLE, msg.sender);
    }

    /*//////////////////////////////////////////////////////////////
                            PRICE FUNCTIONS
    //////////////////////////////////////////////////////////////*/
    
    /**
     * @notice 获取 yToken 相对于 pToken 的当前价格
     * @return 当前价格 (pToken per yToken, 18位精度)
     * @dev 价格获取优先级：
     *      1. 外部价格预言机
     *      2. 资产类型特定的价格逻辑
     *      3. 默认 1:1 比例
     */
    function getCurrentPrice() public view override returns (uint256) {
        // 1. 优先使用外部价格预言机
        if (priceOracle != address(0)) {
            try this._getOraclePrice() returns (uint256 oraclePrice) {
                if (oraclePrice > 0) {
                    return oraclePrice;
                }
            } catch {
                // 预言机失败，继续使用其他方法
            }
        }
        
        // 2. 根据资产类型使用特定逻辑
        if (assetType == AssetType.SUSDS) {
            return _getSUSdsPrice();
        } else if (assetType == AssetType.STETH) {
            return _getStETHPrice();
        }
        
        // 3. 默认返回 1:1 比例
        return PRICE_PRECISION;
    }
    
    /**
     * @notice 从外部预言机获取价格 (外部调用，用于 try-catch)
     * @return 预言机价格
     */
    function _getOraclePrice() external view returns (uint256) {
        require(priceOracle != address(0), "Price oracle not set");
        // 这里应该调用实际的价格预言机接口
        // 例如: return IPriceOracle(priceOracle).getPrice(yToken(), pToken());
        return PRICE_PRECISION; // 临时实现
    }
    
    /**
     * @notice 获取 sUSDS 相对于 USDS 的价格
     * @return sUSDS 价格
     * @dev 实际实现中应该调用 Sky Protocol 的价格接口
     */
    function _getSUSdsPrice() internal view returns (uint256) {
        // 实际实现中应该调用 Sky Protocol 的汇率接口
        // 例如: return ISkyProtocol(skyProtocol).getExchangeRate();
        return PRICE_PRECISION; // 临时 1:1 实现
    }
    
    /**
     * @notice 获取 stETH 相对于 ETH 的价格
     * @return stETH 价格
     * @dev 对于 stETH，通常与 ETH 保持 1:1 比例
     */
    function _getStETHPrice() internal view returns (uint256) {
        // stETH 通常与 ETH 保持 1:1 比例
        // 但可以通过 Chainlink 或其他预言机获取更精确的价格
        return PRICE_PRECISION;
    }

    /*//////////////////////////////////////////////////////////////
                        DEPOSIT/WITHDRAW OVERRIDES
    //////////////////////////////////////////////////////////////*/
    
    /**
     * @notice 存款函数重写 - 增强的存款逻辑
     * @param amount 存入的 yToken 数量
     * @dev 流程：
     *      1. 基础验证和权限检查
     *      2. 费用计算和代币转移
     *      3. 用户份额记录
     *      4. 收益基准线更新 (仅 stETH)
     *      5. 自动收益收集 (如启用)
     */
    function deposit(uint256 amount) external override nonReentrant {
        require(amount > 0, "Amount must be greater than 0");
        require(amount <= maxDeposit(msg.sender), "Exceeds max deposit");
        
        // 调用 DolTroller 检查存款权限
        dolTroller.depositAllowed(address(this), msg.sender, amount);

        // 计算存款费用
        uint256 feeAmount = (amount * depositFee) / 10000;
        uint256 depositAmount = amount - feeAmount;

        // 记录存款前的状态 (用于 stETH 收益计算)
        uint256 balanceBefore = IERC20(yToken()).balanceOf(address(this));

        // 从用户转入 yToken
        IERC20(yToken()).safeTransferFrom(msg.sender, address(this), amount);

        // 记录用户份额 (以 pToken 等价物计价)
        uint256 pTokenEquivalent = yTokenToPToken(depositAmount);
        UserStake memory newStake = UserStake({
            pTokenAmount: pTokenEquivalent,
            timestamp: block.timestamp
        });
        userStakes[msg.sender].push(newStake);
        totalStaked[msg.sender] += pTokenEquivalent;
        totalPTokensStaked += pTokenEquivalent;
        
        // 更新收益基准线 (仅对 stETH)
        if (assetType == AssetType.STETH) {
            uint256 currentBalance = IERC20(yToken()).balanceOf(address(this));
            yieldBaseline = currentBalance;
        }
        
        // 如果启用自动收益收集，尝试收集收益
        if (autoYieldCollection) {
            _tryCollectYield();
        }

        emit Deposit(msg.sender, depositAmount);
    }

    /**
     * @notice 提款函数重写 - 增强的提款逻辑
     * @param amount 提取的 yToken 数量
     */
    function withdraw(uint256 amount) external override nonReentrant {
        require(amount > 0, "Amount must be greater than 0");
        
        // 调用 DolTroller 检查提款权限
        dolTroller.redeemAllowed(address(this), msg.sender, amount);
        
        // 转换为 pToken 等价物进行余额检查
        uint256 pTokenAmount = yTokenToPToken(amount);
        require(pTokenAmount <= maxWithdraw(msg.sender), "Exceeds max withdraw");

        // 计算提款费用
        uint256 feeAmount = (pTokenAmount * withdrawalFee) / 10000;
        uint256 withdrawAmount = pTokenAmount - feeAmount;

        // 从用户份额中扣除
        _removeFromStakes(msg.sender, withdrawAmount);

        // 转账给用户
        IERC20(yToken()).safeTransfer(msg.sender, amount);
        
        // 更新收益基准线 (仅对 stETH)
        if (assetType == AssetType.STETH) {
            uint256 currentBalance = IERC20(yToken()).balanceOf(address(this));
            yieldBaseline = currentBalance;
        }

        emit Withdraw(msg.sender, amount);
    }
