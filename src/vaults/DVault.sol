// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {IERC20Metadata} from "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol";
import {SafeERC20} from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";
import {IDVault} from "../interfaces/IDVault.sol";

import {IDolTroller} from "../interfaces/IDolTroller.sol";

import {IDolTroller} from "../interfaces/IDolTroller.sol";

/**
 * @title DVault
 * @notice Vault that allows users to deposit yTokens and withdraw pTokens
 * @dev Users deposit yield-bearing tokens (yTokens) and can withdraw underlying assets (pTokens)
 */

// TODO: add 4626
abstract contract DVault is IDVault, Ownable {
    using SafeERC20 for IERC20;

    // token addresses
    address private immutable yToken_;
    address private immutable pToken_;

    // Fee configuration
    uint256 public depositFee; // basis points (1/10000)
    uint256 public withdrawalFee; // basis points (1/10000)
    uint256 public buybackFee; // basis points (1/10000)
    uint256 public constant MAX_FEE = 1000; // 10% max fee
    
    uint256 public nextRoundId;
    bool public isCurrentRoundSettled;


    // Buyback configuration
    address public buybackContract;

    // User stake tracking
    struct UserStake {
        uint256 pTokenAmount; // Amount of pTokens representing the user's principal
        uint256 timestamp; // When the stake was created
    }

    mapping(address => UserStake[]) public userStakes;
    mapping(address => uint256) public totalStaked; // Total yTokens staked per user

    // Total tracking
    uint256 public totalPTokensStaked; // Total pTokens representing users' original principal

    IDolTroller public dolTroller;

    modifier onlyDolTroller() {
        require(msg.sender == address(dolTroller), "Only dolTroller");
        _;
    }

    modifier onlyRightRound(uint256 roundId) {
        require(roundId == nextRoundId, "Wrong round id");
        _;
    }

    constructor(
        address _yToken,
        address _pToken,
        IDolTroller _dolTroller
    ) Ownable(msg.sender) {
        require(_yToken != address(0), "invalid yToken");
        require(_pToken != address(0), "invalid pToken");
        yToken_ = _yToken;
        pToken_ = _pToken;
        dolTroller = _dolTroller;
    }

    // ============ Asset Functions ============

    function yToken() external view virtual override returns (address) {
        return yToken_;
    }

    function pToken() external view virtual override returns (address) {
        return pToken_;
    }

    function bToken() public view virtual override returns (address);
    
    // ============ Price Conversion Functions ============

    /**
     * @notice Get the current price of yToken in terms of pToken
     * @return The current price (pToken per yToken)
     */
    function getYTokenPrice() public view virtual returns (uint256);

    /**
     * @notice Convert yToken amount to pToken amount at current price
     * @param yTokenAmount Amount of yTokens
     * @return pTokenAmount Equivalent amount in pTokens
     */
    function yTokenToPToken(uint256 yTokenAmount) public view returns (uint256) {
        uint256 price = getYTokenPrice();
        uint256 yTokenDecimals = IERC20Metadata(yToken_).decimals();
        uint256 pTokenDecimals = IERC20Metadata(pToken_).decimals();
        uint256 scalingFactor = 10 ** (yTokenDecimals + pTokenDecimals);
        return (yTokenAmount * price) / scalingFactor;
    }

    /**
     * @notice Convert pToken amount to yToken amount at current price
     * @param pTokenAmount Amount of pTokens
     * @return yTokenAmount Equivalent amount in yTokens
     */
    function pTokenToYToken(uint256 pTokenAmount) public view returns (uint256) {
        uint256 price = getYTokenPrice();
        uint256 yTokenDecimals = IERC20Metadata(yToken_).decimals();
        uint256 pTokenDecimals = IERC20Metadata(pToken_).decimals();
        uint256 scalingFactor = 10 ** (yTokenDecimals + pTokenDecimals);
        return (pTokenAmount * scalingFactor) / price;
    }

    // ============ User Functions ============

    /**
     * @notice Depost pTokens from the vault
     * @param amount Amount of pTokens to deposit
     */
    function deposit(uint256 amount) external override {
        require(amount > 0, "Amount must be greater than 0");
        require(amount <= maxDeposit(msg.sender), "Exceeds max deposit");
        dolTroller.depositAllowed(address(this), msg.sender, amount);

        // Calculate fee
        uint256 feeAmount = (amount * depositFee) / 10000;
        uint256 depositAmount = amount - feeAmount;

        // Transfer yTokens from user // TODO: check if this is correct, 不是pToken么
        IERC20(yToken_).safeTransferFrom(msg.sender, address(this), amount);

        // Record user stake
        uint256 pTokenEquivalent = yTokenToPToken(depositAmount);
        UserStake memory newStake = UserStake({
            pTokenAmount: pTokenEquivalent,
            timestamp: block.timestamp
        });
        // TODO 一天的充提应该直接合并
        userStakes[msg.sender].push(newStake);
        
        // Track the pToken equivalent as user's principal
        totalStaked[msg.sender] += pTokenEquivalent;
        totalPTokensStaked += pTokenEquivalent;

        emit Deposit(msg.sender, depositAmount); 
    }

    /**
     * @notice Withdraw pTokens from the vault
     * @param amount Amount of pTokens to withdraw
     */
    function withdraw(uint256 amount) external override {
        require(amount > 0, "Amount must be greater than 0");
        dolTroller.redeemAllowed(address(this), msg.sender, amount);

        dolTroller.redeemAllowed(address(this), msg.sender, amount);

        // Convert yToken amount to pToken amount
        uint256 pTokenAmount = yTokenToPToken(amount);
        require(pTokenAmount <= maxWithdraw(msg.sender), "Exceeds max withdraw");

        // Calculate fee on pToken amount
        uint256 feeAmount = (pTokenAmount * withdrawalFee) / 10000;
        uint256 withdrawAmount = pTokenAmount - feeAmount;

        // Remove from user stakes (FILO - First In, Last Out)
        _removeFromStakes(msg.sender, withdrawAmount);

        // Transfer pTokens to user
        IERC20(yToken_).safeTransfer(msg.sender, amount);

        emit Withdraw(msg.sender, amount);
    }

    function approve(address spender, uint256 amount) external override {
        IERC20(yToken_).approve(spender, amount);
    }

    // TODO: add 4626
    // function previewDeposit(uint256 amount) external pure override returns (uint256) {
    //     // For deposits, we return the same amount as users deposit yTokens
    //     return amount;
    // }

    // function previewWithdraw(uint256 amount) external pure override returns (uint256) {
    //     // For withdrawals, we return the yTOken amount
    //     return amount;
    // }

    // ============ User Limits ============

    function maxDeposit(address owner) public view override returns (uint256) {
        // Users can deposit up to their yToken balance
        return IERC20(yToken_).balanceOf(owner);
    }

    function maxWithdraw(address owner) public view override returns (uint256) {
        // Users can withdraw up to their total staked value in pTokens
        return totalStaked[owner];
    }

    // ============ Fee Functions ============

    function setDepositFee(uint256 _depositFee) external override onlyOwner {
        require(_depositFee <= MAX_FEE, "Fee too high");
        depositFee = _depositFee;
    }

    function setWithdrawalFee(uint256 _withdrawalFee) external override onlyOwner {
        require(_withdrawalFee <= MAX_FEE, "Fee too high");
        withdrawalFee = _withdrawalFee;
    }

    function setBuybackFee(uint256 _buybackFee) external override onlyOwner {
        require(_buybackFee <= MAX_FEE, "Fee too high");
        buybackFee = _buybackFee;
    }

    // ============ Buyback Functions ============

    // Virtual functions
    // TODO: what if settlement is failed? Fallback to transfer yToken as yeild to buyback contract
    function settleYield(uint256 roundId, uint256 amountToSettle) public virtual returns (bool);

    function getBTokenAmount(uint256 roundId) public view virtual returns (uint256);


    function setBuybackContract(address buyback) external override onlyOwner {
        buybackContract = buyback;
    }

    function previewSettlement() public view override returns (uint256) {
        // Calculate total current value in pTokens
        uint256 totalValueInPTokens = yTokenToPToken(IERC20(yToken_).balanceOf(address(this)));
        
        // Calculate yield: current value minus original principal
        uint256 totalYield = totalValueInPTokens - totalPTokensStaked;
        // // Apply buyback fee
        // uint256 buybackAmount = (totalYield * buybackFee) / 10000;
        return totalYield;
    }

    function startYieldSettlement(uint256 roundId) external override onlyDolTroller {
        require(isCurrentRoundSettled, "Current round already settled");
        require(roundId == nextRoundId, "Wrong round id");
        isCurrentRoundSettled = false;
        uint256 settlementValue = previewSettlement();
        uint256 amountToSettle = pTokenToYToken(settlementValue);
        isCurrentRoundSettled = settleYield(roundId, amountToSettle);
    }

    function isYieldSettled(uint256 roundId) external view onlyRightRound(roundId) returns (bool) {
        return isCurrentRoundSettled;
    }

    function yieldTransfer(uint256 roundId) external override onlyDolTroller onlyRightRound(roundId) {
        uint256 bTokenAmount = getBTokenAmount(roundId);
        IERC20(bToken()).safeTransfer(buybackContract, bTokenAmount);
        isCurrentRoundSettled = false;
        nextRoundId++;
        emit YieldTransfer(bTokenAmount);
    }

    // ============ Internal Functions ============

    /**
     * @notice Remove pTokens from user stakes using FILO method
     * @param user Address of the user
     * @param amount Amount of pTokens to remove
     */
    function _removeFromStakes(address user, uint256 amount) internal {
        uint256 remainingToRemove = amount;
        UserStake[] storage stakes = userStakes[user];
        
        // TODO: optimize gas efficiency
        for (uint256 i = stakes.length; i > 0 && remainingToRemove > 0; i--) {
            uint256 index = i - 1;
            if (stakes[index].pTokenAmount > 0) {
                uint256 toRemove = stakes[index].pTokenAmount > remainingToRemove ? 
                    remainingToRemove : stakes[index].pTokenAmount;
                
                stakes[index].pTokenAmount -= toRemove;
                remainingToRemove -= toRemove;
                
                // If stake is now empty, remove it from the array
                if (stakes[index].pTokenAmount == 0) {
                    // Move the last element to this position and pop
                    if (index != stakes.length - 1) {
                        stakes[index] = stakes[stakes.length - 1];
                    }
                    stakes.pop();
                }
            }
        }
        
        require(remainingToRemove == 0, "Insufficient staked amount");
        
        // Update total staked
        totalStaked[user] -= amount;
        totalPTokensStaked -= amount;
    }

    // ============ View Functions ============

    /**
     * @notice Get user stake information
     * @param user Address of the user
     * @return stakes Array of user stakes
     */
    function getUserStakes(address user) external view returns (UserStake[] memory) {
        return userStakes[user];
    }

    /**
     * @notice Get the total staked amount for a user
     * @param user Address of the user
     * @return Total staked amount
     */
    function balanceOf(address user) external view returns (uint256) {
        return totalStaked[user];
    }

    /**
     * @notice Get the number of stakes for a user
     * @param user Address of the user
     * @return Number of stakes
     */
    function getUserStakeCount(address user) external view returns (uint256) {
        return userStakes[user].length;
    }

    /**
     * @notice Emergency function to recover stuck tokens
     * @param token Address of the token to recover
     * @param to Address to send tokens to
     * @param amount Amount to recover
     */
    function emergencyRecover(IERC20 token, address to, uint256 amount) external onlyOwner {
        require(to != address(0), "Invalid recipient");
        token.safeTransfer(to, amount);
    }


    function _isDolpinVault() external pure returns (bool) {
        return true;
    }

} 