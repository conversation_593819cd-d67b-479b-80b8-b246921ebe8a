// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {DVault} from "./DVault.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {IERC20Metadata} from "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol";
import {SafeERC20} from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import {IDolTroller} from "../interfaces/IDolTroller.sol";


/**
 * @title DstETH
 * @notice DVault implementation for stETH where bToken is the same as yToken
 * @dev Simple implementation where no settlement or redemption is needed
 */
contract DstETH is DVault {
    using SafeERC20 for IERC20;

    // Token addresses
    address public immutable stETH;
    address public immutable eth;

    uint256 public settlementAmount;


    constructor(
        address _stETH,
        address _eth,
        IDolTroller _dolTroller
    ) DVault(_stETH, _eth, _dolTroller) {
        stETH = _stETH;
        eth = _eth;
    }

    // ============ Asset Functions ============

    function yToken() external view override returns (address) {
        return stETH;
    }

    function pToken() external view override returns (address) {
        return eth;
    }

    function bToken() public view override returns (address) {
        // bToken is the same as yToken for this implementation
        return stETH;
    }

    // ============ Price Functions ============

    /**
     * @notice Get the current price of stETH in terms of ETH
     * @return The current price (ETH per stETH) - always 1:1 for stETH
     */
    function getYTokenPrice() public pure override returns (uint256) {
        // stETH is always 1:1 with ETH for pricing purposes
        return 1 ether;
    }

    // ============ Settlement Functions ============

    /**
     * @notice Settlement function - no actual settlement needed for stETH
     * @param roundId The round ID
     * @param amountToSettle Amount to settle (not used)
     * @return Always returns true as no settlement is needed
     */
    function settleYield(uint256 roundId, uint256 amountToSettle) public override onlyRightRound(roundId) returns (bool) {
        // No settlement needed for stETH, always return true
        settlementAmount = amountToSettle;
        return true;
    }

    /**
     * @notice Get the bToken amount for a specific round
     * @param roundId The round ID
     * @return The amount of bTokens (stETH) available for transfer
     */
    function getBTokenAmount(uint256 roundId) public view override onlyRightRound(roundId) returns (uint256) {
        // Since bToken is yToken (stETH), return the settlement value in yToken terms
        return settlementAmount;
    }

    // ============ View Functions ============

    /**
     * @notice Get the contract name for identification
     * @return The contract name
     */
    function name() external pure returns (string memory) {
        return "DstETH Vault";
    }

    /**
     * @notice Get the contract symbol for identification
     * @return The contract symbol
     */
    function symbol() external pure returns (string memory) {
        return "DstETH";
    }

    /**
     * @notice Get the total assets under management
     * @return Total stETH balance in the contract
     */
    function totalAssets() external view returns (uint256) {
        return IERC20(stETH).balanceOf(address(this));
    }
}