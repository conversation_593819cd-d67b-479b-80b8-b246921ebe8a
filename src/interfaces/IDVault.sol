// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

/**
 * @title IDVault
 * @notice Interface for the DVault contract that manages deposits and withdrawals for a market.
 * @dev This interface is used to interact with the DVault contract.
 */
interface IDVault {
    // Events
    event Deposit(address indexed user, uint256 amount);
    event Withdraw(address indexed user, uint256 amount);
    event Approval(address indexed owner, address indexed spender, uint256 amount);
    event YieldTransfer(uint256 amount);
    
    // assets
    // yToken is the yield-bearing token that users deposit. bToken is the token used in the buyback contract.
    // In the cases of a erc4626 like vault, the yToken is the vault token and the bToken is the underlying asset.
    // In the cases of rebasing token, yToken and bToken are the same.
    /**
     * @notice Get the yToken address
     * @return The yToken address. This is the yield-bearing token that users deposit.
     */
    function yToken() external view returns (address);

    /**
     * @notice Get the pToken address
     * @return The pToken address. This is pricing token of the yToken. Generally, it is the underlying asset of the yToken.
     */
    function pToken() external view returns (address);
    /**
     * @notice Get the bToken address
     * @return The bToken address. This is the token used in the buyback contract. It is either yToken or pToken, depending on the 
     * settlement process.
     */
    function bToken() external view returns (address);

    // User functions
    function deposit(uint256 amount) external;
    function withdraw(uint256 amount) external;
    function approve(address spender, uint256 amount) external;
    // TODO: add 4626
    // function previewDeposit(uint256 amount) external view returns (uint256);
    // function previewWithdraw(uint256 amount) external view returns (uint256);

    // User limits
    function maxDeposit(address owner) external view returns (uint256);
    function maxWithdraw(address owner) external view returns (uint256);

    // Fee functions
    function setDepositFee(uint256 depositFee) external;
    function setWithdrawalFee(uint256 withdrawalFee) external;
    function depositFee() external view returns (uint256);
    function withdrawalFee() external view returns (uint256);
    function setBuybackFee(uint256 buybackFee) external;
    function buybackFee() external view returns (uint256);

    // Buyback funcitons
    /**
     * @notice Set the buyback contract address
     * @param buyback The address of the buyback contract
     */
    function setBuybackContract(address buyback) external;
    /**
     * @notice Preview the amount of pTokens that the current settlement will receive.
     * @return The amount of pTokens that the settlement pool will receive
     */
    function previewSettlement() external view returns (uint256);

    // Yield settlement function
    function startYieldSettlement(uint256 roundId) external;
    
    // TODO: do we need roundId?
    function isYieldSettled(uint256 roundId) external view returns (bool);
    function getBTokenAmount(uint256 roundId) external view returns (uint256);
    
    /**
     * @notice Transfer bToken tokens from the contract to the buyback pool.
     */
    function yieldTransfer(uint256 roundId) external;
}
